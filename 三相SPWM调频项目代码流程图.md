# 三相SPWM调频项目代码流程图

## 项目概述

这是一个基于STM32F407的三相SPWM（正弦脉宽调制）调频闭环控制系统，主要用于逆变器控制。项目实现了三相电压的闭环控制，通过PR（比例谐振）控制器实现高精度的电压跟踪。

## 系统架构

### 硬件平台
- **主控芯片**: STM32F407VET6
- **时钟频率**: 168MHz
- **PWM频率**: 20KHz
- **ADC采样频率**: 20KHz
- **正弦波点数**: 400点

### 主要模块
1. **主控制器模块** (`main.c`)
2. **逆变器控制模块** (`inveter.c/h`)
3. **正弦波生成模块** (`sine.c/h`)
4. **PWM输出模块** (`timer1.c/h`)
5. **ADC采样模块** (`AD.c/h`)
6. **按键控制模块** (`key.c/h`)
7. **显示模块** (`OLED.c/h`)

## 详细代码流程图

### 1. 系统初始化流程

```mermaid
graph TD
    A[系统上电] --> B[初始化逆变器结构体]
    B --> C[生成正弦波查找表]
    C --> D[初始化TIM8和TIM1 PWM通道]
    D --> E[初始化按键、LED、OLED]
    E --> F[初始化串口通信]
    F --> G[初始化TIM3定时器]
    G --> H[初始化ADC和DMA]
    H --> I[配置中断优先级]
    I --> J[进入主循环]
```

### 2. 主循环控制流程

```mermaid
graph TD
    A[主循环开始] --> B[扫描按键状态]
    B --> C{按键状态判断}
    C -->|按键按下且flag=1| D[启动PWM输出]
    C -->|按键释放且flag=0| E[关闭PWM输出]
    D --> F[使能TIM8和TIM1]
    E --> G[禁用TIM8和TIM1]
    G --> H[重置逆变器参数]
    F --> I[继续主循环]
    H --> I
    I --> B
```

### 3. DMA中断处理流程

```mermaid
graph TD
    A[DMA传输完成中断] --> B[设置GPIO为高电平]
    B --> C[读取ADC转换值]
    C --> D[计算线电压和相电压]
    D --> E{flag2是否为真}
    E -->|是| F[计算正弦波索引]
    E -->|否| M[清除中断标志]
    F --> G[计算电压参考值]
    G --> H[执行逆变器闭环控制]
    H --> I[计算PWM占空比]
    I --> J[更新TIM寄存器]
    J --> K[更新索引计数器]
    K --> L[设置GPIO为低电平]
    L --> M
    M --> N[中断返回]
```

### 4. 逆变器闭环控制流程

```mermaid
graph TD
    A[开始闭环控制] --> B[计算电压误差]
    B --> C[PR控制器计算]
    C --> D[更新误差历史值]
    D --> E[计算调制波电压]
    E --> F[限幅处理]
    F --> G[计算B相调制波]
    G --> H[返回控制结果]
```

## 关键算法详解

### 1. PR控制器算法

PR控制器用于实现高精度的电压跟踪，其传递函数为：

```
G(s) = Kp + Kr * s / (s² + ω₀²)
```

在离散域中的实现：
```c
// PR控制器参数 (p=0.2, r=30)
#define A0  0.218744157726683f
#define A1  -0.399850698513033f
#define A2  0.181155873432108f
#define B1  1.999253492565163f
#define B2  -0.999500155793955f

// PR控制器计算
invt->pr_a = A0*invt->ua_err + A1*invt->ua_err1 + A2*invt->ua_err2 + 
              B1*invt->pr_a1 + B2*invt->pr_a2;
```

### 2. 电压计算算法

```c
// 线电压计算
invt.eab = (ADC_Value[0] * 0.000805f - 1.58f) * 32.0f;    // PC0
invt.ebc = (ADC_Value[1] * 0.000805f - 1.68f) * 31.6f;    // PA1

// 相电压计算
invt.ea = (2.0f * invt.eab + invt.ebc) * 0.333333f;
invt.ec = (2.0f * invt.ebc + invt.eab) * -0.333333f;
```

### 3. SPWM调制算法

```c
// 正弦波参考值计算
invt.ea_ref = invt.vref * sinData[i];
invt.ec_ref = invt.vref * sinData[k];

// PWM占空比计算
TIM8->CCR1 = (1 + sinData[i]) * 1050;  // A相
TIM8->CCR2 = (1 + sinData[j]) * 1050;  // B相
TIM1->CCR3 = (1 + sinData[k]) * 1050;  // C相
```

## 关键参数配置

### 1. 定时器配置
- **TIM3**: 20KHz触发ADC采样
- **TIM8**: PWM输出，频率20KHz
- **TIM1**: PWM输出，频率20KHz

### 2. ADC配置
- **采样通道**: 4个通道
  - PC0: 线电压EAB
  - PA1: 线电压EBC
  - PC2: 相电流IA
  - PC3: 相电流IC
- **采样频率**: 20KHz
- **分辨率**: 12位

### 3. PWM配置
- **载波频率**: 20KHz
- **死区时间**: TIM8=0x2F, TIM1=0x4F
- **调制方式**: 中心对齐PWM

## 系统特点

### 1. 闭环控制
- 采用PR控制器实现高精度电压跟踪
- 实时电压反馈控制
- 动态响应性能优良

### 2. 高精度采样
- 20KHz高速ADC采样
- DMA传输减少CPU负担
- 多通道同步采样

### 3. 灵活的控制接口
- 按键控制启停
- OLED显示系统状态
- 串口通信调试接口

### 4. 安全保护
- PWM死区时间保护
- 电压限幅保护
- 系统状态监控

## 应用场景

1. **三相逆变器控制**
2. **变频器应用**
3. **电力电子实验平台**
4. **电机驱动系统**

## 调试建议

1. **波形观察**: 使用示波器观察PWM波形和输出电压
2. **参数调节**: 根据实际负载调整PR控制器参数
3. **性能优化**: 根据应用需求调整采样频率和PWM频率
4. **安全测试**: 确保过压、过流保护功能正常

## 总结

该项目实现了一个完整的三相SPWM闭环控制系统，具有以下优势：

- **高精度控制**: PR控制器提供优异的跟踪性能
- **实时性好**: 20KHz高速采样和控制
- **可靠性高**: 完善的保护机制
- **扩展性强**: 模块化设计便于功能扩展

该系统可作为电力电子控制的基础平台，适用于各种三相逆变器应用场景。 