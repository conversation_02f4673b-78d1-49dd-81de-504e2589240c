..\obj\vofa.o: ..\HARDWARE\vofa\vofa.c
..\obj\vofa.o: ..\SYSTEM\sys\sys.h
..\obj\vofa.o: ..\USER\stm32f4xx.h
..\obj\vofa.o: ..\CORE\core_cm4.h
..\obj\vofa.o: D:\keilarm\ARM\ARMCC\Bin\..\include\stdint.h
..\obj\vofa.o: ..\CORE\core_cmInstr.h
..\obj\vofa.o: ..\CORE\core_cmFunc.h
..\obj\vofa.o: ..\CORE\core_cm4_simd.h
..\obj\vofa.o: ..\USER\system_stm32f4xx.h
..\obj\vofa.o: ..\USER\stm32f4xx_conf.h
..\obj\vofa.o: ..\FWLIB\inc\stm32f4xx_adc.h
..\obj\vofa.o: ..\USER\stm32f4xx.h
..\obj\vofa.o: ..\FWLIB\inc\stm32f4xx_crc.h
..\obj\vofa.o: ..\FWLIB\inc\stm32f4xx_dbgmcu.h
..\obj\vofa.o: ..\FWLIB\inc\stm32f4xx_dma.h
..\obj\vofa.o: ..\FWLIB\inc\stm32f4xx_exti.h
..\obj\vofa.o: ..\FWLIB\inc\stm32f4xx_flash.h
..\obj\vofa.o: ..\FWLIB\inc\stm32f4xx_gpio.h
..\obj\vofa.o: ..\FWLIB\inc\stm32f4xx_i2c.h
..\obj\vofa.o: ..\FWLIB\inc\stm32f4xx_iwdg.h
..\obj\vofa.o: ..\FWLIB\inc\stm32f4xx_pwr.h
..\obj\vofa.o: ..\FWLIB\inc\stm32f4xx_rcc.h
..\obj\vofa.o: ..\FWLIB\inc\stm32f4xx_rtc.h
..\obj\vofa.o: ..\FWLIB\inc\stm32f4xx_sdio.h
..\obj\vofa.o: ..\FWLIB\inc\stm32f4xx_spi.h
..\obj\vofa.o: ..\FWLIB\inc\stm32f4xx_syscfg.h
..\obj\vofa.o: ..\FWLIB\inc\stm32f4xx_tim.h
..\obj\vofa.o: ..\FWLIB\inc\stm32f4xx_usart.h
..\obj\vofa.o: ..\FWLIB\inc\stm32f4xx_wwdg.h
..\obj\vofa.o: ..\FWLIB\inc\misc.h
..\obj\vofa.o: ..\FWLIB\inc\stm32f4xx_cryp.h
..\obj\vofa.o: ..\FWLIB\inc\stm32f4xx_hash.h
..\obj\vofa.o: ..\FWLIB\inc\stm32f4xx_rng.h
..\obj\vofa.o: ..\FWLIB\inc\stm32f4xx_can.h
..\obj\vofa.o: ..\FWLIB\inc\stm32f4xx_dac.h
..\obj\vofa.o: ..\FWLIB\inc\stm32f4xx_dcmi.h
..\obj\vofa.o: ..\FWLIB\inc\stm32f4xx_fsmc.h
..\obj\vofa.o: ..\SYSTEM\usart\usart.h
..\obj\vofa.o: D:\keilarm\ARM\ARMCC\Bin\..\include\stdio.h
..\obj\vofa.o: ..\HARDWARE\vofa\vofa.h
