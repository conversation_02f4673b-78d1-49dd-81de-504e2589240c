#include "sys.h"
#include "delay.h"
#include "usart.h"
#include "led.h"
#include "vofa.h"
#include "AD.h"
#include "OLED.h"
#include "key.h"
#include "timer1.h"
#include "inveter.h"

uint32_t switch_ = 0, flag = 0, flag2 = 0, i = 0;
uint8_t data[4]; 			//����һ���СΪ4��8λ���飬���ڴ��ڷ��͸����͸�vofa

inveter_typedef invt;

float vofa_f[5];		//vofa�����������飨��СΪ�������߸�����

int main(void)
{
    // ��ʼ�����ṹ��
    invt_init(&invt);
	
		// ��ȡ���ұ�����
    get_sin_tab1(PointMax, 1.0f);
	
		// ��ʼ��TIM8��TIM1��PWMͨ��
    TIM8_PWM_CHN_Init(2100 - 1, 2 - 1);
    TIM1_PWM_CHN_Init(2100 - 1, 2 - 1);
	
    KEY_Init();
    LED_Init();
    delay_init(168);		//��ʱ��ʼ��
    OLED_Init();

    uart_init(2000000);	//���ڳ�ʼ��������Ϊ115200

    MyTIM3_Init(2100, 2);		//20KHz
    MyADC1_Init();
    MyDMA1_Init();
    TIM_Cmd(TIM3, ENABLE);	//ʹ�ܶ�ʱ�� ��ʼ����ADC����

    NVIC_PriorityGroupConfig(NVIC_PriorityGroup_2);//����ϵͳ�ж����ȼ�����2

    while(1)
    {
//			//*����
//        vofa_f[0] = invt.eab;							//*>
//        vofa_f[1] = invt.ebc;
//        //		vofa_f[2] = sinData[k];
//        //		vofa_f[3] = pfc.beta;

//        send_to_vofa2(vofa_f, data, 2);
			
        switch_ = KEY_Scan3(0, switch_);

				// �������״̬Ϊ1�ұ�־λflagΪ1������PWM���
        if(switch_ == 1 && flag == 1)				//*>����pwm����flag��ֹ�ظ������ر�pwm
        {
            flag = 0;
            flag2 = 1;
            TIM_ARRPreloadConfig(TIM8, ENABLE);
            TIM_Cmd(TIM8, ENABLE);
            TIM_CtrlPWMOutputs(TIM8, ENABLE);
            TIM_ARRPreloadConfig(TIM1, ENABLE);
            TIM_Cmd(TIM1, ENABLE);
            TIM_CtrlPWMOutputs(TIM1, ENABLE);
        }											//��<

				// �������״̬Ϊ0�ұ�־λflagΪ0����ر�PWM���
        if(switch_ == 0 && flag == 0)				//*>�ر�pwm
        {
            flag = 1;
            flag2 = 0;
            invt_init(&invt);
            TIM_Cmd(TIM3, ENABLE);
            TIM_ARRPreloadConfig(TIM8, DISABLE);
            TIM_Cmd(TIM8, DISABLE);
            TIM_CtrlPWMOutputs(TIM8, DISABLE);
            TIM8->CCR1 = 0;
            TIM8->CCR2 = 0;
            TIM1->CCR3 = 0;
            i = 0;
        }											

        KEY_Calculate(0);
    }
}


void DMA2_Stream0_IRQHandler(void)
{
    if(DMA_GetITStatus(DMA2_Stream0, DMA_IT_TCIF0) != RESET)
    {
        static uint32_t  n = 0, flag3 = 0, k = 0;
        int j;
        static float max = 0, min = 0, a = 0;
        float duty1 = 0, duty2 = 0;
//        float vofa_f[5];		//vofa�����������飨��СΪ�������߸�����

        GPIO_SetBits(GPIOB, GPIO_Pin_1); //����Ϊ�ߵ�ƽ

				// ����ADC����ֵ�����ߵ�ѹ�����ѹ�������ֵ
        invt.eab = (ADC_Value[0] * 0.000805f - 1.58f) * 32.0f;   	//PC0
        invt.ebc = (ADC_Value[1] * 0.000805f - 1.68f) * 31.6f;		//PA1
//			invt.ebc = (ADC_Value[1] * 0.000805f - 1.58f) * 32.0f;		//PA1
        invt.ea = (2.0f * invt.eab + invt.ebc) * 0.333333f;
        invt.ec = (2.0f * invt.ebc + invt.eab) * -0.333333f;
        invt.ia = (ADC_Value[2] * 0.000805f - 1.65f) * 5.0f;			//
        invt.ic = (ADC_Value[3] * 0.000805f - 1.645f) * 4.9f;

        // �����־λflag2Ϊ�棬�������䴦��
        if(flag2)									   		//*>
        {

            k = i + 133;
            if(k >= 400)	k -= 400;
            j = i - 133;
            if(j < 0)	j += 400;
						
						// �������ѹ�ο�ֵ
            invt.ea_ref = invt.vref * sinData[i];
            invt.ec_ref = invt.vref * sinData[k];
          
						// �������ѭ����������			
						invt_loop(&invt);
					
            TIM8->CCR1 = (1 + sinData[i]) * 1050;
            TIM8->CCR2 = (1 + sinData[j]) * 1050;
            TIM1->CCR3 = (1 + sinData[k]) * 1050;

//            //*spwm
//            TIM8->CCR1 = (1 + invt.vm_a) * 1050;
//            TIM8->CCR2 = (1 + invt.vm_b) * 1050;
//            TIM1->CCR3 = (1 + invt.vm_c) * 1050;

            //����SVPWM���ֵ���ö�ʱ����CCR�Ĵ���
//						TIM8->CCR1 = invt.svm_a * 2100;
//						TIM8->CCR2 = invt.svm_b * 2100;
//						TIM1->CCR3 = invt.svm_c * 2100;
            i += 1;
            if(i >= 400)	i = 0;
        }											   		//��<

        //*����
//        vofa_f[0] = invt.eab;							//*>
//        vofa_f[1] = invt.ebc;
////				vofa_f[2] = invt.ea;
////				vofa_f[0] = invt.ea;							//*>
////        vofa_f[1] = invt.ec;
////        //		vofa_f[2] = sinData[k];

//        send_to_vofa2(vofa_f, data, 2);

        GPIO_ResetBits(GPIOB, GPIO_Pin_1); //����Ϊ�͵�ƽ
        DMA_ClearITPendingBit(DMA2_Stream0, DMA_IT_TCIF0);
    }
}
