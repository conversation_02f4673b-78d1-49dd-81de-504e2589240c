/*
 * STM32F4三相SPWM逆变器控制系统主程序
 * 功能：实现基于PR控制器的三相逆变器闭环电压控制
 * 硬件平台：STM32F407VET6
 * 控制频率：20KHz
 * 输出频率：50Hz三相正弦波
 * 控制算法：PR控制器 + SPWM调制
 */

#include "sys.h"        // 系统配置头文件
#include "delay.h"      // 延时函数头文件
#include "usart.h"      // 串口通信头文件
#include "led.h"        // LED指示灯头文件
#include "vofa.h"       // VOFA上位机通信头文件
#include "AD.h"         // ADC采样头文件
#include "OLED.h"       // OLED显示屏头文件
#include "key.h"        // 按键检测头文件
#include "timer1.h"     // PWM定时器头文件
#include "inveter.h"    // 逆变器控制算法头文件

// 全局控制变量定义
uint32_t switch_ = 0;   // 按键状态标志：0=停止状态，1=启动状态
uint32_t flag = 0;      // 系统运行标志：0=系统运行中，1=系统停止
uint32_t flag2 = 0;     // PWM输出使能标志：0=PWM禁用，1=PWM使能
uint32_t i = 0;         // 正弦波查找表索引：0-399循环，对应A相0°基准

uint8_t data[4];        // VOFA上位机数据发送缓冲区（8位数组）
inveter_typedef invt;   // 逆变器控制结构体实例，包含所有控制参数
float vofa_f[5];        // VOFA浮点数据数组，用于实时监控调试

/*
 * 主函数：系统初始化和主循环控制
 * 功能：完成所有外设初始化，然后进入主循环等待按键控制
 */
int main(void)
{
    // 步骤1：初始化逆变器控制结构体，清零所有控制参数
    invt_init(&invt);

    // 步骤2：生成400点正弦波查找表，幅值为1.0，用于SPWM调制
    get_sin_tab1(PointMax, 1.0f);

    // 步骤3：初始化PWM定时器，ARR=2099，PSC=1，载波频率20KHz
    TIM8_PWM_CHN_Init(2100 - 1, 2 - 1);  // TIM8控制A相和B相PWM输出
    TIM1_PWM_CHN_Init(2100 - 1, 2 - 1);  // TIM1控制C相PWM输出

    // 步骤4：初始化外设模块
    KEY_Init();                           // 按键检测初始化
    LED_Init();                           // LED指示灯初始化
    delay_init(168);                      // 延时函数初始化，系统时钟168MHz
    OLED_Init();                          // OLED显示屏初始化

    // 步骤5：串口通信初始化，波特率2Mbps，用于高速数据传输
    uart_init(2000000);

    // 步骤6：ADC采样系统初始化
    MyTIM3_Init(2100, 2);                 // TIM3定时器初始化，20KHz触发频率
    MyADC1_Init();                        // ADC1初始化，4通道采样配置
    MyDMA1_Init();                        // DMA初始化，自动传输ADC数据
    TIM_Cmd(TIM3, ENABLE);                // 启动TIM3定时器，开始ADC采样

    // 步骤7：配置中断优先级分组为2，支持4级抢占优先级和4级子优先级
    NVIC_PriorityGroupConfig(NVIC_PriorityGroup_2);

    // 主循环：系统状态机控制
    while(1)
    {
        // VOFA调试数据发送（已注释）
        // 可用于实时监控线电压、相电压等关键参数
//        vofa_f[0] = invt.eab;                     // 线电压AB
//        vofa_f[1] = invt.ebc;                     // 线电压BC
//        vofa_f[2] = sinData[k];                   // 正弦波参考
//        vofa_f[3] = pfc.beta;                     // PFC相关参数
//        send_to_vofa2(vofa_f, data, 2);           // 发送到上位机

        // 扫描按键状态，实现启动/停止切换
        switch_ = KEY_Scan3(0, switch_);

        // 系统启动逻辑：按键按下且系统处于停止状态时启动PWM输出
        if(switch_ == 1 && flag == 1)              // 启动条件：按键=1且停止标志=1
        {
            flag = 0;                               // 清除停止标志，进入运行状态
            flag2 = 1;                              // 设置PWM使能标志

            // 启动TIM8定时器（A相和B相PWM）
            TIM_ARRPreloadConfig(TIM8, ENABLE);     // 使能ARR预装载
            TIM_Cmd(TIM8, ENABLE);                  // 启动定时器
            TIM_CtrlPWMOutputs(TIM8, ENABLE);       // 使能PWM输出

            // 启动TIM1定时器（C相PWM）
            TIM_ARRPreloadConfig(TIM1, ENABLE);     // 使能ARR预装载
            TIM_Cmd(TIM1, ENABLE);                  // 启动定时器
            TIM_CtrlPWMOutputs(TIM1, ENABLE);       // 使能PWM输出
        }

        // 系统停止逻辑：按键释放且系统处于运行状态时停止PWM输出
        if(switch_ == 0 && flag == 0)              // 停止条件：按键=0且运行标志=0
        {
            flag = 1;                               // 设置停止标志
            flag2 = 0;                              // 清除PWM使能标志

            invt_init(&invt);                       // 重新初始化控制参数，防止积分饱和
            TIM_Cmd(TIM3, ENABLE);                  // 保持ADC采样定时器运行

            // 停止TIM8定时器并清零PWM占空比
            TIM_ARRPreloadConfig(TIM8, DISABLE);    // 禁用ARR预装载
            TIM_Cmd(TIM8, DISABLE);                 // 停止定时器
            TIM_CtrlPWMOutputs(TIM8, DISABLE);      // 禁用PWM输出

            // 清零所有PWM通道的占空比，确保输出为0
            TIM8->CCR1 = 0;                         // A相PWM占空比清零
            TIM8->CCR2 = 0;                         // B相PWM占空比清零
            TIM1->CCR3 = 0;                         // C相PWM占空比清零

            i = 0;                                  // 重置正弦波查找表索引
        }

        KEY_Calculate(0);                           // 按键状态处理函数
    }
}


void DMA2_Stream0_IRQHandler(void)
{
    if(DMA_GetITStatus(DMA2_Stream0, DMA_IT_TCIF0) != RESET)
    {
        static uint32_t  n = 0, flag3 = 0, k = 0;
        int j;
        static float max = 0, min = 0, a = 0;
        float duty1 = 0, duty2 = 0;
//        float vofa_f[5];		//vofa�����������飨��СΪ�������߸�����

        GPIO_SetBits(GPIOB, GPIO_Pin_1); //����Ϊ�ߵ�ƽ

				// ����ADC����ֵ�����ߵ�ѹ�����ѹ�������ֵ
        invt.eab = (ADC_Value[0] * 0.000805f - 1.58f) * 32.0f;   	//PC0
        invt.ebc = (ADC_Value[1] * 0.000805f - 1.68f) * 31.6f;		//PA1
//			invt.ebc = (ADC_Value[1] * 0.000805f - 1.58f) * 32.0f;		//PA1
        invt.ea = (2.0f * invt.eab + invt.ebc) * 0.333333f;
        invt.ec = (2.0f * invt.ebc + invt.eab) * -0.333333f;
        invt.ia = (ADC_Value[2] * 0.000805f - 1.65f) * 5.0f;			//
        invt.ic = (ADC_Value[3] * 0.000805f - 1.645f) * 4.9f;

        // �����־λflag2Ϊ�棬�������䴦��
        if(flag2)									   		//*>
        {

            k = i + 133;
            if(k >= 400)	k -= 400;
            j = i - 133;
            if(j < 0)	j += 400;
						
						// �������ѹ�ο�ֵ
            invt.ea_ref = invt.vref * sinData[i];
            invt.ec_ref = invt.vref * sinData[k];
          
						// �������ѭ����������			
						invt_loop(&invt);
					
            TIM8->CCR1 = (1 + sinData[i]) * 1050;
            TIM8->CCR2 = (1 + sinData[j]) * 1050;
            TIM1->CCR3 = (1 + sinData[k]) * 1050;

//            //*spwm
//            TIM8->CCR1 = (1 + invt.vm_a) * 1050;
//            TIM8->CCR2 = (1 + invt.vm_b) * 1050;
//            TIM1->CCR3 = (1 + invt.vm_c) * 1050;

            //����SVPWM���ֵ���ö�ʱ����CCR�Ĵ���
//						TIM8->CCR1 = invt.svm_a * 2100;
//						TIM8->CCR2 = invt.svm_b * 2100;
//						TIM1->CCR3 = invt.svm_c * 2100;
            i += 1;
            if(i >= 400)	i = 0;
        }											   		//��<

        //*����
//        vofa_f[0] = invt.eab;							//*>
//        vofa_f[1] = invt.ebc;
////				vofa_f[2] = invt.ea;
////				vofa_f[0] = invt.ea;							//*>
////        vofa_f[1] = invt.ec;
////        //		vofa_f[2] = sinData[k];

//        send_to_vofa2(vofa_f, data, 2);

        GPIO_ResetBits(GPIOB, GPIO_Pin_1); //����Ϊ�͵�ƽ
        DMA_ClearITPendingBit(DMA2_Stream0, DMA_IT_TCIF0);
    }
}
