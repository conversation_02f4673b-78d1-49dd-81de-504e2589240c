-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\CORE -I ..\SYSTEM\delay -I ..\SYSTEM\sys -I ..\SYSTEM\usart -I ..\USER -I ..\HARDWARE\LED -I ..\FWLIB\inc -I ..\HARDWARE\ad -I ..\HARDWARE\sine -I ..\HARDWARE\vofa -I ..\HARDWARE\oled -I ..\DSP_LIB -I ..\DSP_LIB\Include -I ..\HARDWARE\pfc -I ..\HARDWARE\spwm -I ..\HARDWARE\KEY -I ..\HARDWARE\show -I ..\HARDWARE\inveter
-ID:\keilarm\ARM\PACK\Keil\STM32F4xx_DFP\2.17.1\Drivers\CMSIS\Device\ST\STM32F4xx\Include
-ID:\keilarm\ARM\PACK\Keil\STM32F4xx_DFP\2.17.1\Device\Include
-D__UVISION_VERSION="542" -DSTM32F407xx -DSTM32F40_41xxx -DUSE_STDPERIPH_DRIVER -DARM_MATH_CM4 -D__CC_ARM -D__ARM_MATH_MATRIX_CHECK -DARM_MATH_ROUNDING -D__TARGET_FPU_VFP -DFPU_PRESENT="1" -D_FPU_USED="1"
-o ..\obj\main.o --omf_browse ..\obj\main.crf --depend ..\obj\main.d "main.c"